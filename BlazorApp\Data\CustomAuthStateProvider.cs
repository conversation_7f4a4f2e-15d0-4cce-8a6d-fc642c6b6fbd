using System.Security.Claims;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;

namespace BlazorApp.Data;

public class CustomAuthStateProvider : AuthenticationStateProvider
{
    private readonly ProtectedLocalStorage _protectedLocalStorage;

    public CustomAuthStateProvider(ProtectedLocalStorage protectedLocalStorage)
    {
        _protectedLocalStorage = protectedLocalStorage;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        try
        {
            var userInfo = await _protectedLocalStorage.GetAsync<UserInfo>("userInfo");
            if (userInfo.Success && userInfo.Value != null && userInfo.Value.IsLoggedIn)
            {
                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, userInfo.Value.Username),
                    new Claim(ClaimTypes.Email, userInfo.Value.Email),
                    new Claim("UserId", userInfo.Value.Id.ToString())
                };

                var identity = new ClaimsIdentity(claims, "CustomAuth");
                var user = new ClaimsPrincipal(identity);
                return new AuthenticationState(user);
            }
        }
        catch
        {
            // Em caso de erro, retorna usuário não autenticado
        }

        return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
    }

    public void NotifyAuthenticationStateChanged()
    {
        NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
    }
}
