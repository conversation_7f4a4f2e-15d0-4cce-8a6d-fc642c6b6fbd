{"GlobalPropertiesHash": "XoZFBQ4X7g03j7c24T0WIaQIQ81T9VohivIpRdXPbig=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["8Cv89YBBCccljwMARzkGT1HHuxFn6sP3Opn9BCjneRU=", "Dki69OhuzkG5wEolKW9pyCNuB7X/6kjFQRZEWwZNsAo=", "aWM5T5VS9yovfH0y3xTtTE/BDw1ItlaSJyRv08EhX14=", "T+eEstef9jGue+C8iYkbvtUmDwrPW0pWg/GMBTECGgI=", "f7iPQjHR5zOknUb8MlMOecD0RDfrHlGJER0qqMIrmZA=", "g7KoS+E1WLTizhOoe7SlcoyReK4Q/qRnj6SpClV8hbU=", "b8XvB96HSjPI8znJvuw38ziQc0gz3Z7X3XR0+D3Dlmk=", "ldsWsSjHxh8a1cluw+hsuHt7abAbuGPQM7EtNuTYj04=", "vMrT68X7K0H8Ec21YhFKHGL4KewVjuORwTwoa6nuqt8=", "t45D8kkAGx5aBsNM8PpRYveEBuExC3nI6H6G4wyeFtA=", "qZknKwfzsrBjVU9T55QNtJqliyxH0rXBjRShrZAuKLc=", "0CnVsePOJ86zldEbz3jhw7awRspz1njysL7KFbg0bYI=", "7f75QEQtAQqp1tN/fZ7zxL+YcSaJ8z/pnOSGTWtbHgE=", "StqeqYn3z/cTSIEEKgHxc9yEm440q2/m56Yn+M31kg4=", "SgqghOfzq72bDgTtLldytWnFxpWeLPuZuP2VFH5LnBU=", "3Uelu+vr/J7of04w8XmMlxuJL+iBoJ7Z0OHDvhkw9gk=", "zd/K04Wbj/g79bUVwCTKGiGv7xNq7+xodKE2fKugQ6E=", "dSbDzYhMHz/3zAh/zTeWpuVOHBM5dMZY2ELJSg1vocc=", "u0qAoKq/A2AoZBC5SkFX50NpBukqcfHtFkxZyL51Aas=", "D3A4xJYuSrd3oTbjIeeScwrq9PoV5po6hKZeoDaNTYk=", "XDTsPjx2oajOFA3rakrfwVnO4MvRZRS1EJApc+hJxtQ=", "rEydpZWO4Y5/mvI6xDsZRBP8buzcBxqBXv69SGqv7Ac=", "j546uHj5hN2eVQgNMBpyamL186uoxannp6diNPVYIIo=", "Eo5wy+OCR3zJygww4gRgfLi3gMkC6nG0fhSdEH8jRnI=", "scKDLlygFLNo3Ie5LV0cUrPQrCsmLP7AP3+Ulz1kLvg=", "fcNG8ENb1W6pZb09vChsyTTemP2soJzxh51HtCMW/2A=", "9IIfaUfCh7X4/qccZHf5Uz/20KnsJ8Pku5WOVZL6QUk=", "Y91/gOiZ2twl0QLu35ACaIiKV0WcAeWBjE58q7tVi0E=", "DJJ3xdcbjt/LfI0ue64FwrnbD6tSvZlovEDe6WxLnbw=", "rbGppLbjAuIDkD1LA6Ia3tNyIuwz6u/Rv8rX8T+IP5s=", "Nt74TJPDETa77YpTvQHvep1p2y5RjAYYv2Bp1lFlnBk=", "XJJ589/vvvBleRj5mUBr37+yvpxsfFdLFCmPen2s9fw=", "z2QO672e/MxUUF1uaAFcyHe5UAFuqV9IyvRnJkYyOOg=", "jOJFLKqA83DtUtsYbLyP0lgPcShUbvvYF+e5/7PM854=", "I34hVaYYAhjtllIj1MIWLFB8g/As3KCtzt76M3OZ3eY=", "zYsVXlhSNJZeQO4lMAd99RJKiOcrRgMQgcmnJ58v6qI=", "T7mvv1BUtqwpcQSZ/dmXuMkxDYRzHlzZHSggzoe0GeQ=", "mXCNo6y85+DLnygBjgwANdKXCwiFnYEpEjJnK5roLm8=", "6L7o7VUPkDPsrMI+enrnqG82gBuDqNm9JKZaeS5m8V8=", "6MHeEBcKoKd1GVSLcHJ4wXmUbHkEyLlj+rV75w7Enhg=", "KzCG2tqbTVIxVnB32wrma7/y7VQHkRCBgpm6BvYO2x4=", "Pkd0M+Ty5bqA1K2tYN0Y+/JF+s1mnnq+nxqqPCrSr98=", "tIXS6hOycq80CQwM8Pns8YaCQDBKY2PsH2DPSYtWP+Q=", "/KWVx1HBKYSwz/SzTWq4OefsiwLvWsCAV9qM5lyYivo=", "ylTrX36lVzkIZns7pQoim6dhq353Z8GEYKSFJ508ihc=", "Z7NP1xKdMhytpGOVp7du7GYChYS7uC0fhwitAcrCvZQ=", "x64p6rkxc11urAWgeNMY60ou0HEi3Mx7P0bIXvvmTVk=", "X1Muf9D/DbQBLEfTbGhkH3Ahpku8YOo/0yNqLfFfcko=", "sfErKTFfpd4cq2kqmzKvnJJEcsZX8iEtRQdecFAXQFA=", "BeV2zryAzTNULCWX415tO1Heq4UwSDGSNidXsJq3YWM=", "LYuHHXQmyvVnJl7Yjx7+EKD2gMQMxpcw6t7gUjyetQ8=", "Zzb3zsDMKPRqeJlXTb2VLJtaXXpvomXBrTFYBuVkq7Q=", "GZgLWGcOJ7EIomHs/Rdcw4G8KuXtj3zmYbV9xdT1KJU=", "CC0NNqnZuL9O6UZIMUmcOQQKmOgOC9uC37haHDuNMas=", "8zrxAH2szot+fYldEdYL3/Usq1Qy7Z4h1CWk4fXunao=", "NdClIyvovVgB62pFZBCQhr1rfROVQfiTxtCVAPq6FZc=", "Xv2VNbP1eMJKnsvECa46RiO5szaJb2V6q6arOdfsbTg=", "Y6kpZfia0xxHbu33Nmoq3BAjfliw5UXEnWzorUF5qtI=", "3kqHybNrggob2Ib2ITo2tQe7qmMhH08SeHNGX665t4U=", "ytC8QeNw+SwQQUOgs+eYAPmqAzxWlJ9zHHekWLAPTrU=", "WgF5itQb68Ba8jtylVP8IvR5vPSuVzqeeQ/XWt2rivQ="], "CachedAssets": {"8Cv89YBBCccljwMARzkGT1HHuxFn6sP3Opn9BCjneRU=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\app.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khy4lop6wu", "Integrity": "aNPcLFwdCCGS2v1guSR64Htd4Ly5uclT7taAptnMPbs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2900, "LastWriteTime": "2025-06-25T13:11:17.9975194+00:00"}, "Dki69OhuzkG5wEolKW9pyCNuB7X/6kjFQRZEWwZNsAo=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\favicon.png", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-25T13:11:18.0091933+00:00"}, "aWM5T5VS9yovfH0y3xTtTE/BDw1ItlaSJyRv08EhX14=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-06-25T13:11:18.2714587+00:00"}, "T+eEstef9jGue+C8iYkbvtUmDwrPW0pWg/GMBTECGgI=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-06-25T13:11:18.2939347+00:00"}, "f7iPQjHR5zOknUb8MlMOecD0RDfrHlGJER0qqMIrmZA=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-06-25T13:11:18.3293426+00:00"}, "g7KoS+E1WLTizhOoe7SlcoyReK4Q/qRnj6SpClV8hbU=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-06-25T13:11:18.3546563+00:00"}, "b8XvB96HSjPI8znJvuw38ziQc0gz3Z7X3XR0+D3Dlmk=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-06-25T13:11:18.3785054+00:00"}, "ldsWsSjHxh8a1cluw+hsuHt7abAbuGPQM7EtNuTYj04=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-06-25T13:11:18.4056416+00:00"}, "vMrT68X7K0H8Ec21YhFKHGL4KewVjuORwTwoa6nuqt8=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-06-25T13:11:18.435307+00:00"}, "t45D8kkAGx5aBsNM8PpRYveEBuExC3nI6H6G4wyeFtA=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-06-25T13:11:18.4559123+00:00"}, "qZknKwfzsrBjVU9T55QNtJqliyxH0rXBjRShrZAuKLc=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-06-25T13:11:18.4772931+00:00"}, "0CnVsePOJ86zldEbz3jhw7awRspz1njysL7KFbg0bYI=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-06-25T13:11:18.4970904+00:00"}, "7f75QEQtAQqp1tN/fZ7zxL+YcSaJ8z/pnOSGTWtbHgE=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-06-25T13:11:18.5257631+00:00"}, "StqeqYn3z/cTSIEEKgHxc9yEm440q2/m56Yn+M31kg4=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-06-25T13:11:18.5372802+00:00"}, "SgqghOfzq72bDgTtLldytWnFxpWeLPuZuP2VFH5LnBU=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-06-25T13:11:18.5543011+00:00"}, "3Uelu+vr/J7of04w8XmMlxuJL+iBoJ7Z0OHDvhkw9gk=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-06-25T13:11:18.5836206+00:00"}, "zd/K04Wbj/g79bUVwCTKGiGv7xNq7+xodKE2fKugQ6E=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-06-25T13:11:18.6126406+00:00"}, "dSbDzYhMHz/3zAh/zTeWpuVOHBM5dMZY2ELJSg1vocc=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-06-25T13:11:18.6304004+00:00"}, "u0qAoKq/A2AoZBC5SkFX50NpBukqcfHtFkxZyL51Aas=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-06-25T13:11:18.6460462+00:00"}, "D3A4xJYuSrd3oTbjIeeScwrq9PoV5po6hKZeoDaNTYk=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-06-25T13:11:18.6908497+00:00"}, "XDTsPjx2oajOFA3rakrfwVnO4MvRZRS1EJApc+hJxtQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-06-25T13:11:18.7113616+00:00"}, "rEydpZWO4Y5/mvI6xDsZRBP8buzcBxqBXv69SGqv7Ac=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-06-25T13:11:18.7279413+00:00"}, "j546uHj5hN2eVQgNMBpyamL186uoxannp6diNPVYIIo=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-06-25T13:11:18.7468025+00:00"}, "Eo5wy+OCR3zJygww4gRgfLi3gMkC6nG0fhSdEH8jRnI=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-06-25T13:11:18.7776801+00:00"}, "scKDLlygFLNo3Ie5LV0cUrPQrCsmLP7AP3+Ulz1kLvg=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-06-25T13:11:18.8033877+00:00"}, "fcNG8ENb1W6pZb09vChsyTTemP2soJzxh51HtCMW/2A=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-06-25T13:11:18.8224777+00:00"}, "9IIfaUfCh7X4/qccZHf5Uz/20KnsJ8Pku5WOVZL6QUk=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-06-25T13:11:18.8514296+00:00"}, "Y91/gOiZ2twl0QLu35ACaIiKV0WcAeWBjE58q7tVi0E=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-06-25T13:11:18.9076453+00:00"}, "DJJ3xdcbjt/LfI0ue64FwrnbD6tSvZlovEDe6WxLnbw=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-06-25T13:11:18.9422743+00:00"}, "rbGppLbjAuIDkD1LA6Ia3tNyIuwz6u/Rv8rX8T+IP5s=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-06-25T13:11:18.9932638+00:00"}, "Nt74TJPDETa77YpTvQHvep1p2y5RjAYYv2Bp1lFlnBk=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-06-25T13:11:19.0471778+00:00"}, "XJJ589/vvvBleRj5mUBr37+yvpxsfFdLFCmPen2s9fw=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-06-25T13:11:19.0896242+00:00"}, "z2QO672e/MxUUF1uaAFcyHe5UAFuqV9IyvRnJkYyOOg=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-06-25T13:11:19.146744+00:00"}, "jOJFLKqA83DtUtsYbLyP0lgPcShUbvvYF+e5/7PM854=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-06-25T13:11:19.1829373+00:00"}, "I34hVaYYAhjtllIj1MIWLFB8g/As3KCtzt76M3OZ3eY=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-06-25T13:11:19.2389809+00:00"}, "zYsVXlhSNJZeQO4lMAd99RJKiOcrRgMQgcmnJ58v6qI=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-06-25T13:11:19.2698393+00:00"}, "T7mvv1BUtqwpcQSZ/dmXuMkxDYRzHlzZHSggzoe0GeQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-06-25T13:11:19.3329658+00:00"}, "mXCNo6y85+DLnygBjgwANdKXCwiFnYEpEjJnK5roLm8=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-06-25T13:11:19.3542539+00:00"}, "6L7o7VUPkDPsrMI+enrnqG82gBuDqNm9JKZaeS5m8V8=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-06-25T13:11:19.4149301+00:00"}, "6MHeEBcKoKd1GVSLcHJ4wXmUbHkEyLlj+rV75w7Enhg=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-06-25T13:11:19.4309718+00:00"}, "KzCG2tqbTVIxVnB32wrma7/y7VQHkRCBgpm6BvYO2x4=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-06-25T13:11:19.4769522+00:00"}, "Pkd0M+Ty5bqA1K2tYN0Y+/JF+s1mnnq+nxqqPCrSr98=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-06-25T13:11:19.4974108+00:00"}, "tIXS6hOycq80CQwM8Pns8YaCQDBKY2PsH2DPSYtWP+Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-06-25T13:11:19.5480667+00:00"}, "/KWVx1HBKYSwz/SzTWq4OefsiwLvWsCAV9qM5lyYivo=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-06-25T13:11:19.5697734+00:00"}, "ylTrX36lVzkIZns7pQoim6dhq353Z8GEYKSFJ508ihc=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-06-25T13:11:19.6204037+00:00"}, "Z7NP1xKdMhytpGOVp7du7GYChYS7uC0fhwitAcrCvZQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-06-25T13:11:19.6411237+00:00"}}, "CachedCopyCandidates": {}}