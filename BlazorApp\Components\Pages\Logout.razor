@page "/logout"
@inject NavigationManager Navigation
@inject IHttpContextAccessor HttpContextAccessor
@inject BlazorApp.Data.UserInfo UserInfo
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@rendermode InteractiveServer

<PageTitle>Logout</PageTitle>

<div style="display: flex; flex-direction: column; align-items: center; margin-top: 3rem;">
    <h2>Sessão encerrada</h2>
    <button class="enviar-btn" @onclick="VoltarHome">Voltar para Home</button>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        // Fazer logout
        if (HttpContextAccessor.HttpContext != null)
        {
            await HttpContextAccessor.HttpContext.SignOutAsync(
                CookieAuthenticationDefaults.AuthenticationScheme);
        }

        // Limpar UserInfo
        UserInfo.Username = string.Empty;
        UserInfo.Email = string.Empty;
        UserInfo.Id = 0;
        UserInfo.IsLoggedIn = false;
    }

    private void VoltarHome()
    {
        Navigation.NavigateTo("/");
    }
}