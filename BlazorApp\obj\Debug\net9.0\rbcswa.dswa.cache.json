{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["wfKbUWb3W4x+sEAfWStMqrV7QSb6ZDXItM5jJy3TlUc=", "miZ2+jVsAato6PtNpx9t+j34Cm1wFlNQz34EXJ11TCc=", "RHoumK0+6Bez87Q69s5dExCKnyCNZlht24VFTUreWgI=", "sRWshPiaoriwffgkaGulzzI1lu0zzjoPsw9R/BguNzE=", "A3bJb3v1qVgUEavv0ZWSGmgJ2ufkfXMCnOUma2Vb864=", "mVHmY5iUDpIinFSnZC+rCaVKYGDZhDjUUhHw/gkpGd4=", "Qm5bF40j3+I6Kah4ldH1ga0BKv89/sjjA+8p7dbAcUk=", "36mNdhZLZYqTO9QmpPl2j7U5Risja15pPdSSb50gxfI=", "yqs62RRuEf3O+3vAXTXFc4NmHQnek516c6/5Qt7u2vM=", "nYVOVi0dfzucyiCFi1C0Gz1ADyAxk5e1MUxQxElFl4M=", "3B5ZpP5S+RmWL+1/86RH8HXBFjXghoaiMFIBewqJeh0=", "0gj4WeSjS9lAIN3LPy8nXsh1OqiP2oyNQltTafFAAJE=", "azgZrjTddAZk9SoNbFBtKo/o3eH4ZBwYXrNEThALLG8=", "j90PEEpaoXhdOKPSNh9/EYKA06t5ttq3d8kAIgLYA2U=", "eu1zIJxYdT2OjNSQFYqulLJCTEmIBHof1uctEvZhNJg=", "EX5EytcH6uyAoWZbfLV8nJWID1KLgjx4u7QAg3in1Zw=", "eLihvwNmT4b3mQWExZVSTamArxL0fz0HhReIpgnlKxc=", "QmawmJl4e6OnYe39NEABOvWezaD4a9/Iv+vPXjSWuHY=", "eaizlJ0Gs3JMjSktzI+eaUTTl9FRvjnLuLe1FMNOOaw=", "li0ZTSsMg+SxNGQrj5MP71dcd7YXVtN7czh2Rf7skvU=", "9T1OS+J60k64AvuQsC5g9ltPjpA2pRZ+3bHJRHLHwjc=", "JtUIMlMoV1d/1h7fSj9lHC0eCVDoy+92arPZwYJZdaI=", "wg6dOi5Qig9cLn5DESD7wlyvdO9/Pny/20hCQPhc+Ag=", "ZPzKZTvktM7WDz9VKtCQLd3NcNC35fUFo+fvmNvf8EU=", "4InA3WDiJkGkOquVgv78fBU84dnibpBC8br0tIZbG/M=", "Z1ho1nDFJyMl+98bcUMB+6n4ewVIAQAeEHcO8dR66mA=", "ZmAX71FWwTPO/LXd8+xgSYm2L0NTWsvEhH15IZz4RHI=", "J3CNqKXQOQmQ33CHraVvHjBA1bttJU78OFUirngGbJ4=", "M5lPVvbAqi7faV5Chhb1GIyPqOMPufqFJyY5ohu8XM8=", "uQ/RZyVskoGvqe65c+9WoMJWUpDTCja4gPhUQaIBZ34=", "sEjtUQSzima2ZbgfKwFZGxY5Z7J8ELW+TYcnvxufU5k=", "QH37EKndDnE1O/BKyavzHi02xYGHzBNF2JydQjOrhMc=", "xshbeLHFicxGFa7CB/ZM0ZdVhdoVzhiyr5hvvAvrMqM=", "8JuA82zYzZEAtwZ1gB1j2o5l1UqkfxdKCQIQsn9ANwk=", "j6bsMoDxmOSx0cwjSIrpgkrC5cAPgYPgXED8l6VVqh8=", "tkGc0boUsaYXLyidRYw1EP3ucimH1B7KCDtwwM+SBuQ=", "hWThlXRMVUnn6Wn8W8OJgF48VV78GUdPCjMI+chKSJQ=", "QbcoVAUlZEFcVSQX2AcHe+UaguOiEG4VhYIO5HX3JpM=", "E75rwnGQ7lwc5KXISP0s7WtH1sakv9jN16+7Pas4kjs=", "Sa5a8vvglHI+3ZtDiVccMdVIbRXje6+Put2nPyCsdl0=", "eQj2fE5HQlpk29CgN8OGpaB83LRysyjrZI4PZpyykR0=", "sC3PYSAwwgZeYDFeRWGggMm1a/M+kQMLtlecGLe4Igo=", "hrSKHmryw4owD0q20BcNWZkHUfPIU8Fa8nIbsxo17ik=", "Sh9ptyogj5Ai6q9rNCXdmKVc+sIdteC7FSFK1qn5tX8=", "Ah28UsHhwq8hkkPzmHvqQftB7cTh4eVGOAC8vTXHqL4=", "W2LrdzDfs1f8RjEbHmDAXVftVFy5PoagiJGULwDL3K4=", "E22mk1PBDPaHx73GZpN4RhbwXgUI2fg+Ea9u9ZAN2ro="], "CachedAssets": {"E22mk1PBDPaHx73GZpN4RhbwXgUI2fg+Ea9u9ZAN2ro=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\wo77s772mi-etcmw3a2ax.gz", "SourceId": "BlazorApp", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "BlazorApp#[.{fingerprint=etcmw3a2ax}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\BlazorApp.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dasig06coy", "Integrity": "tc9sbDu7UFhOI0t/tAn+SeRH1pWh0xPJ+Ul9a6YaMbE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\BlazorApp.bundle.scp.css", "FileLength": 1919, "LastWriteTime": "2025-07-10T12:20:12.8102079+00:00"}, "W2LrdzDfs1f8RjEbHmDAXVftVFy5PoagiJGULwDL3K4=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\io2m3ndci8-etcmw3a2ax.gz", "SourceId": "BlazorApp", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "BlazorApp#[.{fingerprint=etcmw3a2ax}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\BlazorApp.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dasig06coy", "Integrity": "tc9sbDu7UFhOI0t/tAn+SeRH1pWh0xPJ+Ul9a6YaMbE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\BlazorApp.styles.css", "FileLength": 1919, "LastWriteTime": "2025-07-10T12:20:12.7703887+00:00"}, "Ah28UsHhwq8hkkPzmHvqQftB7cTh4eVGOAC8vTXHqL4=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\opd9y4aud5-0j3bgjxly4.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-10T12:20:12.8513913+00:00"}, "Sh9ptyogj5Ai6q9rNCXdmKVc+sIdteC7FSFK1qn5tX8=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\kxietpdl1t-63fj8s7r0e.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-10T12:20:12.7915935+00:00"}, "hrSKHmryw4owD0q20BcNWZkHUfPIU8Fa8nIbsxo17ik=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\5njhqh7xtn-h1s4sie4z3.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-10T12:20:12.6526421+00:00"}, "sC3PYSAwwgZeYDFeRWGggMm1a/M+kQMLtlecGLe4Igo=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\0u96xy8fvl-notf2xhcfb.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-10T12:20:12.4969259+00:00"}, "eQj2fE5HQlpk29CgN8OGpaB83LRysyjrZI4PZpyykR0=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\sr3h4f93uo-y7v9cxd14o.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-10T12:20:12.3266415+00:00"}, "Sa5a8vvglHI+3ZtDiVccMdVIbRXje6+Put2nPyCsdl0=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\xsrupkryqx-jj8uyg4cgr.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-10T12:20:12.7915935+00:00"}, "E75rwnGQ7lwc5KXISP0s7WtH1sakv9jN16+7Pas4kjs=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\0saptgtj9d-kbrnm935zg.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-10T12:20:12.6196303+00:00"}, "QbcoVAUlZEFcVSQX2AcHe+UaguOiEG4VhYIO5HX3JpM=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\6dfujon9zs-vr1egmr9el.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-10T12:20:12.5200814+00:00"}, "hWThlXRMVUnn6Wn8W8OJgF48VV78GUdPCjMI+chKSJQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\p42wmf8otl-iovd86k7lj.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-10T12:20:12.4340202+00:00"}, "tkGc0boUsaYXLyidRYw1EP3ucimH1B7KCDtwwM+SBuQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\13lffo5t1f-493y06b0oq.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-10T12:20:12.3212475+00:00"}, "j6bsMoDxmOSx0cwjSIrpgkrC5cAPgYPgXED8l6VVqh8=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\1sz49rmwkj-6pdc2jztkx.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-10T12:20:12.7865918+00:00"}, "8JuA82zYzZEAtwZ1gB1j2o5l1UqkfxdKCQIQsn9ANwk=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\fq48dsgicn-6cfz1n2cew.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-10T12:20:12.6805897+00:00"}, "xshbeLHFicxGFa7CB/ZM0ZdVhdoVzhiyr5hvvAvrMqM=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\mlnodvukef-ft3s53vfgj.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-10T12:20:12.6162293+00:00"}, "QH37EKndDnE1O/BKyavzHi02xYGHzBNF2JydQjOrhMc=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\e3b6vot6pv-pk9g2wxc8p.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-10T12:20:12.4808862+00:00"}, "sEjtUQSzima2ZbgfKwFZGxY5Z7J8ELW+TYcnvxufU5k=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\ab17yshpgm-hrwsygsryq.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-10T12:20:12.3448147+00:00"}, "uQ/RZyVskoGvqe65c+9WoMJWUpDTCja4gPhUQaIBZ34=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\0xtiojayxm-37tfw0ft22.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-10T12:20:12.8188427+00:00"}, "M5lPVvbAqi7faV5Chhb1GIyPqOMPufqFJyY5ohu8XM8=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\t9iux5uy80-v0zj4ognzu.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-10T12:20:12.7494784+00:00"}, "J3CNqKXQOQmQ33CHraVvHjBA1bttJU78OFUirngGbJ4=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\zwblozsce1-46ein0sx1k.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-10T12:20:12.6332781+00:00"}, "ZmAX71FWwTPO/LXd8+xgSYm2L0NTWsvEhH15IZz4RHI=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\1hxfx8n7zp-pj5nd1wqec.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-10T12:20:12.5827856+00:00"}, "Z1ho1nDFJyMl+98bcUMB+6n4ewVIAQAeEHcO8dR66mA=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\haity7p0ag-s35ty4nyc5.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-10T12:20:12.3266415+00:00"}, "4InA3WDiJkGkOquVgv78fBU84dnibpBC8br0tIZbG/M=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\cucqbafjfu-nvvlpmu67g.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-10T12:20:12.7224806+00:00"}, "ZPzKZTvktM7WDz9VKtCQLd3NcNC35fUFo+fvmNvf8EU=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\ktk28wh8eu-06098lyss8.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-10T12:20:12.6614047+00:00"}, "wg6dOi5Qig9cLn5DESD7wlyvdO9/Pny/20hCQPhc+Ag=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\n5r8ievuwg-j5mq2jizvt.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-10T12:20:12.8459536+00:00"}, "JtUIMlMoV1d/1h7fSj9lHC0eCVDoy+92arPZwYJZdaI=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\4fbcuqaia4-tdbxkamptv.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-10T12:20:12.4549631+00:00"}, "9T1OS+J60k64AvuQsC5g9ltPjpA2pRZ+3bHJRHLHwjc=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\thpyd883u6-c2oey78nd0.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-10T12:20:12.3212475+00:00"}, "li0ZTSsMg+SxNGQrj5MP71dcd7YXVtN7czh2Rf7skvU=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\h8lbyf2ser-lcd1t2u6c8.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-10T12:20:12.6186327+00:00"}, "eaizlJ0Gs3JMjSktzI+eaUTTl9FRvjnLuLe1FMNOOaw=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\007zkxsw8o-r4e9w2rdcm.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-10T12:20:12.5059277+00:00"}, "QmawmJl4e6OnYe39NEABOvWezaD4a9/Iv+vPXjSWuHY=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\x5ccnekf8u-khv3u5hwcm.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-10T12:20:12.4666243+00:00"}, "eLihvwNmT4b3mQWExZVSTamArxL0fz0HhReIpgnlKxc=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\owwlybu3zo-jd9uben2k1.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-10T12:20:12.372326+00:00"}, "EX5EytcH6uyAoWZbfLV8nJWID1KLgjx4u7QAg3in1Zw=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\r5yx1cjafz-dxx9fxp4il.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-10T12:20:12.3095969+00:00"}, "eu1zIJxYdT2OjNSQFYqulLJCTEmIBHof1uctEvZhNJg=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\ka2hslmig3-ee0r1s7dh0.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-10T12:20:12.7169563+00:00"}, "j90PEEpaoXhdOKPSNh9/EYKA06t5ttq3d8kAIgLYA2U=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\yolid6ymez-rzd6atqjts.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-10T12:20:12.6614047+00:00"}, "azgZrjTddAZk9SoNbFBtKo/o3eH4ZBwYXrNEThALLG8=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\yzhuma1lfw-fsbi9cje9m.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-10T12:20:12.471995+00:00"}, "0gj4WeSjS9lAIN3LPy8nXsh1OqiP2oyNQltTafFAAJE=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\k4zki3r4ay-b7pk76d08c.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-10T12:20:12.3962965+00:00"}, "3B5ZpP5S+RmWL+1/86RH8HXBFjXghoaiMFIBewqJeh0=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\jhpk321h38-fvhpjtyr6v.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-10T12:20:12.3212475+00:00"}, "nYVOVi0dfzucyiCFi1C0Gz1ADyAxk5e1MUxQxElFl4M=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\2yjm2z46op-ub07r2b239.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-10T12:20:12.6482776+00:00"}, "yqs62RRuEf3O+3vAXTXFc4NmHQnek516c6/5Qt7u2vM=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\7ewgskmfsj-cosvhxvwiu.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-10T12:20:12.5559771+00:00"}, "36mNdhZLZYqTO9QmpPl2j7U5Risja15pPdSSb50gxfI=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\60rx4zdl21-k8d9w2qqmf.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-10T12:20:12.4850756+00:00"}, "Qm5bF40j3+I6Kah4ldH1ga0BKv89/sjjA+8p7dbAcUk=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\ox1s4qcnz1-ausgxo2sd3.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-10T12:20:12.3839198+00:00"}, "mVHmY5iUDpIinFSnZC+rCaVKYGDZhDjUUhHw/gkpGd4=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\8qkq4vyj1x-d7shbmvgxk.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-10T12:20:12.3212475+00:00"}, "A3bJb3v1qVgUEavv0ZWSGmgJ2ufkfXMCnOUma2Vb864=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\nm2a4evlxf-aexeepp0ev.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-10T12:20:12.5539773+00:00"}, "sRWshPiaoriwffgkaGulzzI1lu0zzjoPsw9R/BguNzE=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\8nurldnakx-erw9l3u2r3.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-10T12:20:12.4404037+00:00"}, "RHoumK0+6Bez87Q69s5dExCKnyCNZlht24VFTUreWgI=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\rr9cuthk2h-c2jlpeoesf.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-10T12:20:12.4194469+00:00"}, "miZ2+jVsAato6PtNpx9t+j34Cm1wFlNQz34EXJ11TCc=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\4fjygrhgud-bqjiyaj88i.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-10T12:20:12.3571616+00:00"}, "wfKbUWb3W4x+sEAfWStMqrV7QSb6ZDXItM5jJy3TlUc=": {"Identity": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\niqfivrdr6-khy4lop6wu.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "app#[.{fingerprint=khy4lop6wu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "596e5mu3rg", "Integrity": "KfTtGgOr4gFqWP6bDC3erwKnY0fchsCFGM8SFQeyGO0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\app.css", "FileLength": 1571, "LastWriteTime": "2025-07-10T12:20:12.3086045+00:00"}}, "CachedCopyCandidates": {}}