@page "/login"
@inject NavigationManager Navigation
@inject BlazorApp.Data.UserInfo UserInfo
@inject ApplicationDbContext DbContext
@using BlazorApp.Data
@using Microsoft.EntityFrameworkCore
@rendermode InteractiveServer

<PageTitle>Login</PageTitle>

<div style="display: flex; flex-direction: column; align-items: center; margin-top: 3rem;">
    <h2>Login</h2>
    <input @bind="username" placeholder="Username ou Email" style="margin-bottom: 1rem; padding: 0.5rem; width: 250px;" />
    <input @bind="password" placeholder="Senha" type="password" style="margin-bottom: 1rem; padding: 0.5rem; width: 250px;" />
    <button class="enviar-btn" @onclick="LoginAsync">Entrar</button>
    @if (!string.IsNullOrEmpty(erro))
    {
        <div style="color: #c0392b; margin-top: 1rem;">@erro</div>
    }
</div>

@code {
    private string? username;
    private string? password;
    private string? erro;

    private async Task LoginAsync()
    {
        erro = string.Empty;

        try
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
            {
                erro = "Por favor, preencha todos os campos.";
                return;
            }

            // Buscar usuário no banco de dados
            var user = await DbContext.Users.FirstOrDefaultAsync(u => 
                (u.Username == username || u.Email == username) && u.Password == password);

            if (user != null)
            {
                // Atualizar o estado de login do usuário
                user.IsLoggedIn = true;
                await DbContext.SaveChangesAsync();

                // Atualizar o UserInfo da sessão
                UserInfo.Username = user.Username;
                UserInfo.Email = user.Email;
                UserInfo.Id = user.Id;
                UserInfo.IsLoggedIn = true;

                // Redirecionar para a página inicial
                Navigation.NavigateTo("/");
            }
            else
            {
                erro = "Usuário ou senha inválidos.";
            }
        }
        catch (Exception ex)
        {
            erro = "Erro ao realizar login: " + ex.Message;
        }
    }
}
