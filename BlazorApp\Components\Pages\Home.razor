﻿@page "/"
@rendermode InteractiveServer
@inject BlazorApp.Data.UserInfo UserInfo
@inject ApplicationDbContext DbContext
@using BlazorApp.Data
@using Microsoft.EntityFrameworkCore
@inject NavigationManager Navigation

<PageTitle>Home</PageTitle>

<style>
    .message-text {
        white-space: pre-wrap;
        word-break: break-word;
    }
    .table > :not(caption) > * > * {
        padding: 1rem;
    }
    .btn-primary {
        background-color: #3a0647;
        border-color: #3a0647;
    }
    .btn-primary:hover {
        background-color: #2a0435;
        border-color: #2a0435;
    }
</style>

<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">

<div class="container mt-4">
    <div class="card">
        <div class="card-body">
            <div class="form-group">
                <textarea @bind="userText" class="form-control" placeholder="Digite sua mensagem..." rows="4" style="resize: none; border-radius: 8px; border-color: #3a0647;"></textarea>
            </div>
            <button class="btn btn-primary mt-2" style="background-color: #3a0647; border: none;" @onclick="EnviarMensagem">
                <i class="bi bi-send-fill"></i> Enviar
            </button>
        </div>
    </div>

    @if (mensagens.Any())
    {
        <div class="card mt-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Mensagens</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th scope="col" style="color: #3a0647;">Mensagens Enviadas</th>
                                <th scope="col" style="color: #3a0647;">Autor</th>
                                <th scope="col" class="text-center" style="color: #3a0647;">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (int i = 0; i < mensagens.Count; i++)
                            {
                                <tr>
                                    <td>
                                        @if (editandoIndice == i)
                                        {
                                            <input @bind="mensagemEditada" class="form-control" />
                                        }
                                        else
                                        {
                                            <div class="message-text">@mensagens[i].Texto</div>
                                        }
                                    </td>
                                    <td>
                                        <span class="text-muted">@mensagens[i].CriadoPor.Username</span>
                                    </td>
                                    <td class="text-center">
                                        @if (mensagens[i].CriadoPorId == UserInfo.Id)
                                        {
                                            @if (editandoIndice == i)
                                            {
                                                <button class="btn btn-success btn-sm me-2" @onclick="() => SalvarEdicao(i-1)">
                                                    <i class="bi bi-check-lg"></i> Salvar
                                                </button>
                                                <button class="btn btn-secondary btn-sm" @onclick="CancelarEdicao">
                                                    <i class="bi bi-x-lg"></i> Cancelar
                                                </button>
                                            }
                                            else
                                            {
                                                <button class="btn btn-primary btn-sm me-2" @onclick="() => EditarMensagem(i-1)">
                                                    <i class="bi bi-pencil"></i> Editar
                                                </button>
                                                <button class="btn btn-danger btn-sm" @onclick="async () => await ExcluirMensagem(i-1)">
                                                    <i class="bi bi-trash"></i> Excluir
                                                </button>
                                            }
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private string? userText;
    private List<Mensagem> mensagens = new();
    private int? editandoIndice = null;
    private string? mensagemEditada;

    protected override async Task OnInitializedAsync()
    {
        await CarregarMensagens();
    }

    private async Task CarregarMensagens()
    {
        mensagens = await DbContext.Mensagens
            .Include(m => m.CriadoPor)
            .OrderByDescending(m => m.DataCriacao)
            .ToListAsync();
    }

    private async Task EnviarMensagem()
    {
        if (!string.IsNullOrWhiteSpace(userText) && UserInfo.Id > 0)
        {
            try
            {
                var mensagem = new Mensagem
                {
                    Texto = userText,
                    CriadoPorId = UserInfo.Id,
                    DataCriacao = DateTime.UtcNow
                };

                DbContext.Mensagens.Add(mensagem);
                await DbContext.SaveChangesAsync();
                
                await CarregarMensagens();
                userText = string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro ao enviar mensagem: {ex.Message}");
            }
        }
    }

    private void EditarMensagem(int indice)
    {
        editandoIndice = indice;
        mensagemEditada = mensagens[indice].Texto;
    }

    private async Task SalvarEdicao(int indice)
    {
        if (!string.IsNullOrWhiteSpace(mensagemEditada))
        {
            var mensagem = mensagens[indice];
            try
            {
                mensagem.Texto = mensagemEditada;
                await DbContext.SaveChangesAsync();
                await CarregarMensagens();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erro ao salvar edição: {ex.Message}");
            }
        }
        editandoIndice = null;
        mensagemEditada = string.Empty;
    }

    private void CancelarEdicao()
    {
        editandoIndice = null;
        mensagemEditada = string.Empty;
    }

    private async Task ExcluirMensagem(int indice)
    {
        var mensagem = mensagens[indice];
        try
        {
            DbContext.Mensagens.Remove(mensagem);
            await DbContext.SaveChangesAsync();
            await CarregarMensagens();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erro ao excluir mensagem: {ex.Message}");
        }
    }
}
