﻿@inherits LayoutComponentBase
@inject NavigationManager Navigation
@inject BlazorApp.Data.UserInfo UserInfo
@inject IHttpClientFactory ClientFactory
@using BlazorApp.Data

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-5">
            <h1 style="text-align: center;">MURAL</h1>
            <a href="https://learn.microsoft.com/aspnet/core/" target="_blank">About</a>
            @* Adicionando botões de autenticação ao header *@
            <div class="auth-buttons" style="display: flex; gap: 0.5rem; margin-left: auto; align-items: center;">
                @if (UserInfo.IsLoggedIn)
                {
                    <span style="margin-right: 1rem; font-weight: 600; color: #3a0647;">@UserInfo.Username</span>
                    <div class="enviar-btn" style="background: #c0392b;"><a href="logout">Sair</a></div>
                }
                else
                {
                    <div class="enviar-btn"><a href="login">Login</a></div>
                    <div class="enviar-btn"><a href="register">Signup</a></div>
                }
            </div>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

