@page "/register"
@inject NavigationManager Navigation
@inject ApplicationDbContext DbContext
@rendermode InteractiveServer
@using BlazorApp.Data
@using Microsoft.EntityFrameworkCore

<h3>Cadastro de Usuários</h3>

<div style="max-width: 400px; margin: 2rem auto;">
    <div class="form-group">
        <label>Username:</label>
        <input class="form-control" @bind="username" />
    </div>
    <div class="form-group">
        <label>Email:</label>
        <input class="form-control" @bind="email" type="email" />
    </div>
    <div class="form-group">
        <label>Senha:</label>
        <input class="form-control" @bind="password" type="password" />
    </div>
    <button class="enviar-btn" @onclick="CadastrarUsuario" style="margin-top: 1rem;">Cadastrar</button>
    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger" role="alert" style="margin-top: 1rem;">
            @errorMessage
        </div>
    }
</div>

@code {
    private string? username;
    private string? email;
    private string? password;
    private string? errorMessage;

    private async Task CadastrarUsuario()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(email) || string.IsNullOrWhiteSpace(password))
            {
                errorMessage = "Por favor, preencha todos os campos.";
                return;
            }

            // Verificar se o usuário já existe
            var userExists = await DbContext.Users.AnyAsync(u => u.Username == username || u.Email == email);
            if (userExists)
            {
                errorMessage = "Username ou email já cadastrado.";
                return;
            }

            var user = new Data.UserInfo
            {
                Username = username,
                Email = email,
                Password = password // Em uma aplicação real, a senha deve ser hash
            };

            DbContext.Users.Add(user);
            await DbContext.SaveChangesAsync();

            // Redirecionar para a página de login após o cadastro
            Navigation.NavigateTo("/login");
        }
        catch (Exception ex)
        {
            errorMessage = "Erro ao cadastrar usuário: " + ex.Message;
        }
    }
}
