{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["uTiK/j3Yviyi68fjCgvhtL9niEKSCj0Wy9Wz5++J4pg=", "lfNddPSrIdUFUpUJBUpR9sJSDhifwRRbXrvyDWkHW4A=", "cbTu9ncyzEu0azF5sRs0vEkcZXrLkqNDowZFiWm3CUc=", "agYZP3Ep3qcNT3P3elafL0k5/ZM+7bPngmvmZXoINFE=", "EqRFl+NRlMiKgkSbfQA5+rbJ/SkdjMr7QvAjyMdFzvE=", "WkTY9+JCRg/jDXj9HcfZ8ljdqm+RzppSk43k5zVbL5Y=", "/j6reiYG9wW+HZ6pdl1gfHg2GHnFZm+quMFWI+lg29c=", "67377isn4QjfQtQ25wVvir1NByVE9YrQd+eU1CGiFa8=", "Im/9eP/2vpfZb9uq/raKfWNv1TcGlpe6ZE1JTaslbz8=", "UIpBzcvVxB8cx0ZpeWHtq3BnzJEFqnik7VSjGIs/J0c=", "i7Ti7Tukkq96lPDsBLdNfb5raXr7QsW5/2mjPKalvCw=", "wWuHWOaTNqb4WttMU8zWz006YQ340sFJlaMPOKm8iPE=", "dNV5eLLiaeUMoUikfBTxmJTvSWK+9NoQYxgEtbsJLp0=", "HAF2+LY+PMpXnyejZ6l5AfWocmzSPsrT+iFV73jZ9f8=", "zZbHjPsKYcTuMEg86WqaIuqY7U9jlexdu/iiS+aHl5M=", "tqpTxg9sfHbN3jqpp27kzPGIM6AqbcIbUvUxFuVmqDo=", "EEppOcCtMZzm7NHQti9kVdquqLN9EWAy/8PU399zgg0=", "iG97EbuiTLV6ZOwk4VSgJ7Nj9UGXh4ZeKaemjunidQk=", "gGRIUcMfBCViYnkBLkJ3glSC4S02QKznRkXLJziwwjI=", "CBfvCD5znKcB65p+dg2ZzuH8FcP33amVQ8PHxMMsESE=", "TeKae54OTZ/I9ni2VqxvmjPPMObfqhiDTBZVAamDBzE=", "9tZdkEj8aE5+QWzQl2HCLW1DP/gfBzEHc+FcEAyBKIg=", "dGrG/WtKGfQqxsN85JOitNqdPJnESOaHMIOAlGUL0KM=", "nx42vXf+sbvUq5kbRyuJ62D8w61u4xYGmbjPLU1Mg3k=", "KPLvNI1zmUZE87X6Tm7KVdeGwPGJiOXunk+Xi6gE22w=", "PjHCNVUa0xnLG7iQA7lbRQqHOAskog4s3LRvtmWV5Vc=", "6V5Z/dM2SgCYwGu+FKfovuIqSrWMtkyzALf+ySIG90Q=", "+fizykaTpRp7C3NCATf6zCl6QDb9qp5czb+/wpKbQmw=", "V1Nra3r23g2N04ON/7sgosLG/KaVzXeAcVbe9EH+40U=", "K6dQqRRpX6ksOLSaGsUpPaq501T7cYq90jhcWBJ2Was=", "TVAF+HZeoqvOAAIJsmPLIpf+DaUorvmEmegc+TPhu4c=", "P7ssQMRySvfxIHJuIXWwTSlUh3e298SosgjgKwkARsM=", "kvB0DCwvUOXE+p0vMZgMsxLDeFKifpb3PX8VBFxV5Y8=", "rTpOsOGLZfUH8oAJszcIKoS2Zt7HQKymGT+fImogIf0=", "jYnbvC1D6GwcYkXBohMKEBbT939+Ke9lcwam5Et6120=", "glyjNJBXOefuU3ee/7TaAPH96ZewaTsK+brG2cWeWyE=", "+67srf4zKqP8e+43W/rDmZ6p6/VXxNQY8FMOUVBSjDI=", "/vbj7MAHEzrtmbaS3zzRxSnEiG5bmYpxYnHmGOlIXCw=", "1MGc6n/+8wpxYJB3bOLI2Y8+PKaFCcrn9cMcnUDCAsI=", "jyAy0Y+hLrUzexrGqaHb84bM/GdonNyrAn9XtmMEt1E=", "InKnHe9T5eKF/1I6kyfU997CeIAGNwLv9i3unIDfG+w=", "TZ0Xy1wJDaizOWq6TwAWUrgIKfDw7NeJqMYIC+bp2OQ=", "4EqCb2UhxEqN88nJltFDKdTVVvgmuk+NEq9yIvec/hU=", "iRV/zSYArwRgxlYSXJ2tcwYkN+AFEaf2t+r9pQVT+3I=", "3ysrS1M9YTWglB45O33D53J6sM0bP5HjMdInpfSvNs0=", "chNEf/rjFL8gbydPRT76/hSVC3ttxUAHxsODIZvbIVw=", "ABmguL/sFJwxou2rmA/Vu7FhvhzInum7uMCiVtHahL0="], "CachedAssets": {"uTiK/j3Yviyi68fjCgvhtL9niEKSCj0Wy9Wz5++J4pg=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\niqfivrdr6-khy4lop6wu.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "app#[.{fingerprint=khy4lop6wu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "596e5mu3rg", "Integrity": "KfTtGgOr4gFqWP6bDC3erwKnY0fchsCFGM8SFQeyGO0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\app.css", "FileLength": 1571, "LastWriteTime": "2025-07-10T11:32:22.8953519+00:00"}, "lfNddPSrIdUFUpUJBUpR9sJSDhifwRRbXrvyDWkHW4A=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\4fjygrhgud-bqjiyaj88i.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-10T11:32:22.9065558+00:00"}, "cbTu9ncyzEu0azF5sRs0vEkcZXrLkqNDowZFiWm3CUc=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\rr9cuthk2h-c2jlpeoesf.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-10T11:32:22.9317677+00:00"}, "agYZP3Ep3qcNT3P3elafL0k5/ZM+7bPngmvmZXoINFE=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\8nurldnakx-erw9l3u2r3.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-10T11:32:22.920623+00:00"}, "EqRFl+NRlMiKgkSbfQA5+rbJ/SkdjMr7QvAjyMdFzvE=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\nm2a4evlxf-aexeepp0ev.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-10T11:32:22.9392023+00:00"}, "WkTY9+JCRg/jDXj9HcfZ8ljdqm+RzppSk43k5zVbL5Y=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\8qkq4vyj1x-d7shbmvgxk.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-10T11:32:22.8823408+00:00"}, "/j6reiYG9wW+HZ6pdl1gfHg2GHnFZm+quMFWI+lg29c=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\ox1s4qcnz1-ausgxo2sd3.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-10T11:32:22.8883411+00:00"}, "67377isn4QjfQtQ25wVvir1NByVE9YrQd+eU1CGiFa8=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\60rx4zdl21-k8d9w2qqmf.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-10T11:32:22.907567+00:00"}, "Im/9eP/2vpfZb9uq/raKfWNv1TcGlpe6ZE1JTaslbz8=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\7ewgskmfsj-cosvhxvwiu.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-10T11:32:22.9216223+00:00"}, "UIpBzcvVxB8cx0ZpeWHtq3BnzJEFqnik7VSjGIs/J0c=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\2yjm2z46op-ub07r2b239.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-10T11:32:22.9258394+00:00"}, "i7Ti7Tukkq96lPDsBLdNfb5raXr7QsW5/2mjPKalvCw=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\jhpk321h38-fvhpjtyr6v.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-10T11:32:22.9412007+00:00"}, "wWuHWOaTNqb4WttMU8zWz006YQ340sFJlaMPOKm8iPE=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\k4zki3r4ay-b7pk76d08c.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-10T11:32:22.8788767+00:00"}, "dNV5eLLiaeUMoUikfBTxmJTvSWK+9NoQYxgEtbsJLp0=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\yzhuma1lfw-fsbi9cje9m.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-10T11:32:22.907567+00:00"}, "HAF2+LY+PMpXnyejZ6l5AfWocmzSPsrT+iFV73jZ9f8=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\yolid6ymez-rzd6atqjts.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-10T11:32:23.0911741+00:00"}, "zZbHjPsKYcTuMEg86WqaIuqY7U9jlexdu/iiS+aHl5M=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\ka2hslmig3-ee0r1s7dh0.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-10T11:32:22.9402019+00:00"}, "tqpTxg9sfHbN3jqpp27kzPGIM6AqbcIbUvUxFuVmqDo=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\r5yx1cjafz-dxx9fxp4il.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-10T11:32:22.9490241+00:00"}, "EEppOcCtMZzm7NHQti9kVdquqLN9EWAy/8PU399zgg0=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\owwlybu3zo-jd9uben2k1.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-10T11:32:22.9578989+00:00"}, "iG97EbuiTLV6ZOwk4VSgJ7Nj9UGXh4ZeKaemjunidQk=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\x5ccnekf8u-khv3u5hwcm.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-10T11:32:22.8813412+00:00"}, "gGRIUcMfBCViYnkBLkJ3glSC4S02QKznRkXLJziwwjI=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\007zkxsw8o-r4e9w2rdcm.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-10T11:32:22.9278362+00:00"}, "CBfvCD5znKcB65p+dg2ZzuH8FcP33amVQ8PHxMMsESE=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\h8lbyf2ser-lcd1t2u6c8.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-10T11:32:22.9634051+00:00"}, "TeKae54OTZ/I9ni2VqxvmjPPMObfqhiDTBZVAamDBzE=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\thpyd883u6-c2oey78nd0.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-10T11:32:22.9757873+00:00"}, "9tZdkEj8aE5+QWzQl2HCLW1DP/gfBzEHc+FcEAyBKIg=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\4fbcuqaia4-tdbxkamptv.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-10T11:32:22.9892826+00:00"}, "dGrG/WtKGfQqxsN85JOitNqdPJnESOaHMIOAlGUL0KM=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\n5r8ievuwg-j5mq2jizvt.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-10T11:32:23.0345386+00:00"}, "nx42vXf+sbvUq5kbRyuJ62D8w61u4xYGmbjPLU1Mg3k=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\ktk28wh8eu-06098lyss8.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-10T11:32:22.8833397+00:00"}, "KPLvNI1zmUZE87X6Tm7KVdeGwPGJiOXunk+Xi6gE22w=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\cucqbafjfu-nvvlpmu67g.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-10T11:32:22.9238377+00:00"}, "PjHCNVUa0xnLG7iQA7lbRQqHOAskog4s3LRvtmWV5Vc=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\haity7p0ag-s35ty4nyc5.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-10T11:32:22.9505002+00:00"}, "6V5Z/dM2SgCYwGu+FKfovuIqSrWMtkyzALf+ySIG90Q=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\1hxfx8n7zp-pj5nd1wqec.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-10T11:32:22.9747863+00:00"}, "+fizykaTpRp7C3NCATf6zCl6QDb9qp5czb+/wpKbQmw=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\zwblozsce1-46ein0sx1k.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-10T11:32:22.993418+00:00"}, "V1Nra3r23g2N04ON/7sgosLG/KaVzXeAcVbe9EH+40U=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\t9iux5uy80-v0zj4ognzu.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-10T11:32:23.0227991+00:00"}, "K6dQqRRpX6ksOLSaGsUpPaq501T7cYq90jhcWBJ2Was=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\0xtiojayxm-37tfw0ft22.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-10T11:32:22.8883411+00:00"}, "TVAF+HZeoqvOAAIJsmPLIpf+DaUorvmEmegc+TPhu4c=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\ab17yshpgm-hrwsygsryq.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-10T11:32:22.9258394+00:00"}, "P7ssQMRySvfxIHJuIXWwTSlUh3e298SosgjgKwkARsM=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\e3b6vot6pv-pk9g2wxc8p.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-10T11:32:22.9470107+00:00"}, "kvB0DCwvUOXE+p0vMZgMsxLDeFKifpb3PX8VBFxV5Y8=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\mlnodvukef-ft3s53vfgj.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-10T11:32:22.9849789+00:00"}, "rTpOsOGLZfUH8oAJszcIKoS2Zt7HQKymGT+fImogIf0=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\fq48dsgicn-6cfz1n2cew.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-07-10T11:32:23.0217992+00:00"}, "jYnbvC1D6GwcYkXBohMKEBbT939+Ke9lcwam5Et6120=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\1sz49rmwkj-6pdc2jztkx.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-07-10T11:32:23.0488486+00:00"}, "glyjNJBXOefuU3ee/7TaAPH96ZewaTsK+brG2cWeWyE=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\13lffo5t1f-493y06b0oq.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-10T11:32:22.8833397+00:00"}, "+67srf4zKqP8e+43W/rDmZ6p6/VXxNQY8FMOUVBSjDI=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\p42wmf8otl-iovd86k7lj.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-07-10T11:32:22.9337663+00:00"}, "/vbj7MAHEzrtmbaS3zzRxSnEiG5bmYpxYnHmGOlIXCw=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\6dfujon9zs-vr1egmr9el.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-07-10T11:32:22.9568897+00:00"}, "1MGc6n/+8wpxYJB3bOLI2Y8+PKaFCcrn9cMcnUDCAsI=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\0saptgtj9d-kbrnm935zg.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-07-10T11:32:22.9777861+00:00"}, "jyAy0Y+hLrUzexrGqaHb84bM/GdonNyrAn9XtmMEt1E=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\xsrupkryqx-jj8uyg4cgr.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-10T11:32:22.9944144+00:00"}, "InKnHe9T5eKF/1I6kyfU997CeIAGNwLv9i3unIDfG+w=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\sr3h4f93uo-y7v9cxd14o.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-07-10T11:32:23.0106031+00:00"}, "TZ0Xy1wJDaizOWq6TwAWUrgIKfDw7NeJqMYIC+bp2OQ=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\0u96xy8fvl-notf2xhcfb.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-07-10T11:32:22.8853395+00:00"}, "4EqCb2UhxEqN88nJltFDKdTVVvgmuk+NEq9yIvec/hU=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\5njhqh7xtn-h1s4sie4z3.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-07-10T11:32:22.9186214+00:00"}, "iRV/zSYArwRgxlYSXJ2tcwYkN+AFEaf2t+r9pQVT+3I=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\kxietpdl1t-63fj8s7r0e.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-10T11:32:22.9347683+00:00"}, "3ysrS1M9YTWglB45O33D53J6sM0bP5HjMdInpfSvNs0=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\opd9y4aud5-0j3bgjxly4.gz", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-07-10T11:32:22.9747863+00:00"}, "chNEf/rjFL8gbydPRT76/hSVC3ttxUAHxsODIZvbIVw=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\io2m3ndci8-etcmw3a2ax.gz", "SourceId": "BlazorApp", "SourceType": "Computed", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "BlazorApp#[.{fingerprint=etcmw3a2ax}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\scopedcss\\bundle\\BlazorApp.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dasig06coy", "Integrity": "tc9sbDu7UFhOI0t/tAn+SeRH1pWh0xPJ+Ul9a6YaMbE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\scopedcss\\bundle\\BlazorApp.styles.css", "FileLength": 1919, "LastWriteTime": "2025-07-10T11:32:22.9867487+00:00"}, "ABmguL/sFJwxou2rmA/Vu7FhvhzInum7uMCiVtHahL0=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\wo77s772mi-etcmw3a2ax.gz", "SourceId": "BlazorApp", "SourceType": "Computed", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\compressed\\", "BasePath": "_content/BlazorApp", "RelativePath": "BlazorApp#[.{fingerprint=etcmw3a2ax}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\scopedcss\\projectbundle\\BlazorApp.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dasig06coy", "Integrity": "tc9sbDu7UFhOI0t/tAn+SeRH1pWh0xPJ+Ul9a6YaMbE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\obj\\x86\\Debug\\net9.0\\scopedcss\\projectbundle\\BlazorApp.bundle.scp.css", "FileLength": 1919, "LastWriteTime": "2025-07-10T11:32:22.9944144+00:00"}}, "CachedCopyCandidates": {}}