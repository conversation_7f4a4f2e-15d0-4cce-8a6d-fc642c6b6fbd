@page "/login"
@inject NavigationManager Navigation
@inject BlazorApp.Data.UserInfo UserInfo
@inject ApplicationDbContext DbContext
@inject IHttpContextAccessor HttpContextAccessor
@inject AuthenticationStateProvider AuthStateProvider
@using BlazorApp.Data
@using Microsoft.EntityFrameworkCore
@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage
@inject ProtectedLocalStorage ProtectedLocalStorage
@attribute [StreamRendering(enabled: false)]

<PageTitle>Login</PageTitle>

<div style="display: flex; flex-direction: column; align-items: center; margin-top: 3rem;">
    <h2>Login</h2>
    <input @bind="username" placeholder="<PERSON>rname ou Email" style="margin-bottom: 1rem; padding: 0.5rem; width: 250px;" />
    <input @bind="password" placeholder="Senha" type="password" style="margin-bottom: 1rem; padding: 0.5rem; width: 250px;" />
    <button class="enviar-btn" @onclick="LoginAsync">Entrar</button>
    @if (!string.IsNullOrEmpty(erro))
    {
        <div style="color: #c0392b; margin-top: 1rem;">@erro</div>
    }
</div>

@code {
    private string? username;
    private string? password;
    private string? erro;

    private async Task LoginAsync()
    {
        erro = string.Empty;

        try
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
            {
                erro = "Por favor, preencha todos os campos.";
                return;
            }

            // Buscar usuário no banco de dados
            Console.WriteLine($"Tentando login com username/email: {username}");
            var user = await DbContext.Users.FirstOrDefaultAsync(u => 
                (u.Username == username || u.Email == username) && u.Password == password);

            if (user != null)
            {
                Console.WriteLine($"Usuário encontrado: {user.Username}");
                try
                {
                    // Criar claims de identidade
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, user.Username),
                        new Claim(ClaimTypes.Email, user.Email),
                        new Claim("UserId", user.Id.ToString())
                    };

                    var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    var authProperties = new AuthenticationProperties
                    {
                        IsPersistent = true,
                        ExpiresUtc = DateTimeOffset.UtcNow.AddDays(30)
                    };

                    if (HttpContextAccessor.HttpContext != null)
                    {
                        Console.WriteLine("HttpContext está disponível, tentando configurar o cookie");
                        
                        try
                        {
                            // Fazer login com cookie
                            await HttpContextAccessor.HttpContext.SignInAsync(
                                CookieAuthenticationDefaults.AuthenticationScheme,
                                new ClaimsPrincipal(claimsIdentity),
                                authProperties);

                            Console.WriteLine("Cookie configurado com sucesso");

                            // Atualizar o UserInfo da sessão
                            UserInfo.Username = user.Username;
                            UserInfo.Email = user.Email;
                            UserInfo.Id = user.Id;
                            UserInfo.IsLoggedIn = true;

                            // Armazenar informações do usuário no localStorage protegido
                            await ProtectedLocalStorage.SetAsync("userInfo", new
                            {
                                user.Username,
                                user.Email,
                                user.Id,
                                IsLoggedIn = true
                            });

                            Console.WriteLine("Login realizado com sucesso, aguardando antes de redirecionar...");
                            
                            // Pequeno delay para garantir que o cookie seja configurado
                            await Task.Delay(500);
                            
                            // Redirecionar para a página inicial
                            Navigation.NavigateTo("/", forceLoad: true);
                    }
                    else
                    {
                        Console.WriteLine("HttpContext não está disponível");
                        erro = "Erro ao realizar login: contexto HTTP não disponível";
                    }
                }
                catch (Exception ex)
                {
                    erro = $"Erro ao realizar login: {ex.Message}";
                }
            }
            else
            {
                erro = "Usuário ou senha inválidos.";
            }
        }
        catch (Exception ex)
        {
            erro = "Erro ao realizar login: " + ex.Message;
        }
    }
}
