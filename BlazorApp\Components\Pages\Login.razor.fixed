@page "/login"
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@using BlazorApp.Data
@using Microsoft.EntityFrameworkCore

@inject NavigationManager Navigation
@inject IHttpContextAccessor HttpContextAccessor
@inject ApplicationDbContext DbContext
@inject BlazorApp.Data.UserInfo UserInfo

<PageTitle>Login</PageTitle>

<div style="display: flex; flex-direction: column; align-items: center; margin-top: 3rem;">
    <h2>Login</h2>
    <input @bind="username" placeholder="Username ou Email" style="margin-bottom: 1rem; padding: 0.5rem; width: 250px;" />
    <input @bind="password" placeholder="Senha" type="password" style="margin-bottom: 1rem; padding: 0.5rem; width: 250px;" />
    <button class="enviar-btn" @onclick="LoginAsync">Entrar</button>
    @if (!string.IsNullOrEmpty(erro))
    {
        <div style="color: #c0392b; margin-top: 1rem;">@erro</div>
    }
</div>

@code {
    private string? username;
    private string? password;
    private string? erro;

    private async Task LoginAsync()
    {
        erro = string.Empty;

        try
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
            {
                erro = "Por favor, preencha todos os campos.";
                return;
            }

            var user = await DbContext.Users.FirstOrDefaultAsync(u => 
                (u.Username == username || u.Email == username) && u.Password == password);

            if (user != null && HttpContextAccessor.HttpContext != null)
            {
                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, user.Username),
                    new Claim(ClaimTypes.Email, user.Email),
                    new Claim("UserId", user.Id.ToString())
                };

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var authProperties = new AuthenticationProperties
                {
                    IsPersistent = true,
                    ExpiresUtc = DateTimeOffset.UtcNow.AddDays(30)
                };

                await HttpContextAccessor.HttpContext.SignInAsync(
                    CookieAuthenticationDefaults.AuthenticationScheme,
                    new ClaimsPrincipal(claimsIdentity),
                    authProperties);

                UserInfo.Username = user.Username;
                UserInfo.Email = user.Email;
                UserInfo.Id = user.Id;
                UserInfo.IsLoggedIn = true;

                Navigation.NavigateTo("/", forceLoad: true);
            }
            else
            {
                erro = "Usuário ou senha inválidos.";
            }
        }
        catch (Exception ex)
        {
            erro = $"Erro ao realizar login: {ex.Message}";
        }
    }
}
