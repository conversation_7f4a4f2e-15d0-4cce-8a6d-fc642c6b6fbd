{"GlobalPropertiesHash": "8q6b8fFOQ6WvVdPK02Glvag0ZdC8tnPWJOAqoyMO648=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["3mF4/DmTfUCAHvp3u9CLg6TdbuDv/YvXhL8HHjnBB5E=", "AbrXC66N7vVh67bt0w+7RJuzQLKI3/l/KMhf+Kzap2g=", "bZOCiOs6B44lpNxCMb3MKHwzYGgQIyDBXwFSHOHbGEk=", "G1SATK442bUYeu1GM7pZBdJKh8UJtCHO9qPO5tO5RYs=", "VbJuZbmngt+QgYUxbDfJA+JYnG/fonNoLEKLhTaZEBc=", "aT3gUgRH1nS5Iy9aaZiWXt24ua2Ud8gTwAdG+B7YsR4=", "S/gr/ANdqnso3HQR/58PbYyEjvWz3ggepSAhFs/7IHw=", "qJSGiZ+LOD7HCab1Vb/UKwkGoDdGWGcCo5JgxrjlVYU=", "A4HVZSFkQ6/tKMn8TAx6jfwc4KDvMrwr4NfUGHUHZU0=", "GE/Oox1hAU/my2nTgutXSWWw5ZARQ6EooJXTA9jv+z4=", "2Tc7Uj+6P6SBAqqXNIrrZw4MUaVJPJopDiF72wTiceM=", "HS3qkQWwMDP71XDIwYtQcAv6OnbxyL7krIVy8/4uZ0s=", "8dINMDJwhsZfw0elhn6kDFBeX/FTrEQqs1kf1113mJo=", "i1JrZ2Y2cA67I9HiLqVMzU6tXyjaiJYWdhnB30Pi4g8=", "26Rlr04EabEiLh6hU4n4ZW8Yr0+p9kASJl59prgURDw=", "VitwRWpZZoZPUXfNf9Hp83gciIeM599u0DjKwg3gU5o=", "Lh9oQBeVj33Rn3ZPxcKgv4p7KxoWCaS6PCVhJTfELno=", "CEXGF2hZFB+ulWW97Sz7p8VSsrN1H0RAYHZRNtjFAxw=", "MCGa2TD5H46YSazxO6jLj6SeTJk3xSRjB2luetJf1J4=", "ZDSkrG1iCuWm2ISNUCyS24oe8/j0mQXj7W7tc6Ekeec=", "VlPW8QxZ7oVCM+oJie+wp2yr3FTVhLJVgiESuIoy09Y=", "m1dNhKck3O6229rFxLoMGo78gyBgmYnlnWdZBjbrdyQ=", "Qum9i1JuEC603bbqfvEm6qA+VjgFs+uJYCIqqkgAd9o=", "enyO9AJTpkX/YpPZAZat4/U+kk/2mgVJiviE24YBePo=", "pCgRdVRSLbilEvGem/m5auKZbS5o24JV2oXq9xzEaro=", "he3X3jND+PTLNMqzIXrTmOAGS0lht0xoBaMecoPqu8k=", "8U9VG6gfgT5LuCc3oqoi+gpfxZI3fF+BtSVw4Ku7spk=", "VgbX58gA3M2IoXaUJBAWx6wCFALgktgzLh0AO8z8M2I=", "5OQft9LuK9PkjpcW/eGazir2Z63OzRtZfZ8dpX6dWKs=", "FPCmC33/I6QnCjp0Hacw1WBWqiEiIpqkaw7dhNZkGxY=", "34QShnI3j7a880YXkyGbL63WNgF1635aKAq7P1OcX1E=", "nFD0XwlxtGmnAyD3r1thdZE1mGXNap7jd4ONnba1SB8=", "CeG85OBnMsqFk9Yua7kLUN6HCP9yoYifrL/TDbu8JuQ=", "T14bMODE6ZTtegpAoWYIdF4pGAZnDPZlw3HvgOfUB8I=", "j5R9qJU592wOQqM/ZuK2gqm+iQIt8i90Ie6MIPKaqKo=", "sR0fFTNoyHbhQQJDlCGSCKFAqPZOfzuj7+NubjUvR/M=", "k+mzzsy4MEusoZH+cO7XGXvA+DWZ94GCJPMB8RtIg4w=", "oGJAXYPRWExJdEnfY0CxA6EyPyxNDwM+gjtzVGSIcj0=", "YtZVcIiLSKfK8iRR0RNWQAY0V5/VfIMcfjOsYyNkI2U=", "EjXbe2X9nv6vV2zeh9ldqVeV0NzRX7/NiQ6MuIoOcIQ=", "oWYKWW9uruOcX3NqgGY6HW/Dw422/0a8wi4h3qo59Eg=", "D/cpjkSTMYJuiLX999UXOmi9Si3T3htICwUboBKe/fg=", "NdvDyx7LOaYuK+4sIO3p67sU8TFlK2dZUgYkgAktgRw=", "twHUabccNXlFp8LRk1jUAxOzqXwmjja5oqBAbhndN14=", "3SqxyYmsM/PFAqlZbqLEz3+GtgGkA+bONwpc9G616D8=", "k7aQwaHZj0sMn+v6+voJm/D/lMY+BFoF5dQcMsJFfmQ=", "dAYBdpIaIVXkq8Z5UQ4UoVDRTTuHv22Ron+M/yCQOg0=", "N/bOvtgMI2zqQKeznxzXxViK720YJdKDrdWoK+G/oSg=", "7LinAsyGdi05uuBxeaX5YVWv2Ki0qC0mbB+iWBWmE/I=", "FV+uMPx31Pk7+0UlcNUbNkw3EnRAgQKhryYL+C67LyI=", "fDZrqxgICL9nSLKhPzpPG4z/3cr3Zh4vxaFqemh1xwg=", "CvbEoHbaLTfEHtEg+XYljG+c+bICXGEbSx9wkYXdudc=", "60k9VkPGVtI/Wm2xEPW79Bdm306YpsypeRWssG3+qms=", "jZ8msZsDkJqBtrY+eUbAqNJm5oT0y3/rMR+0aQyTvtU=", "hSr2aOxv874XHMD649HBb47fCQpUjze5mjct5LVXbx0=", "7Xw2yOEh5tGERmnkNd5EHTgYIKt3phpsuCr6XeNMmAQ=", "tEkBgXYv0ddpNZAh5272OYZsbJ2NJSnCmXVL110ftxw=", "oVICM8sa8WAVcykP/oYRgTqarXs/12GRQseI1uh8RS8=", "l4CjtfuG/g9jHpxNsHjYJoZLkK+T/iZw2EJKeSTWSck=", "x73iuPXDYdfxXAIZDYA6K7dGlVdIknKAitwKUmtw4OE="], "CachedAssets": {"3mF4/DmTfUCAHvp3u9CLg6TdbuDv/YvXhL8HHjnBB5E=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\app.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khy4lop6wu", "Integrity": "aNPcLFwdCCGS2v1guSR64Htd4Ly5uclT7taAptnMPbs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2900, "LastWriteTime": "2025-06-25T13:11:17.9975194+00:00"}, "AbrXC66N7vVh67bt0w+7RJuzQLKI3/l/KMhf+Kzap2g=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\favicon.png", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-25T13:11:18.0091933+00:00"}, "bZOCiOs6B44lpNxCMb3MKHwzYGgQIyDBXwFSHOHbGEk=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-06-25T13:11:18.2714587+00:00"}, "G1SATK442bUYeu1GM7pZBdJKh8UJtCHO9qPO5tO5RYs=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-06-25T13:11:18.2939347+00:00"}, "VbJuZbmngt+QgYUxbDfJA+JYnG/fonNoLEKLhTaZEBc=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-06-25T13:11:18.3293426+00:00"}, "aT3gUgRH1nS5Iy9aaZiWXt24ua2Ud8gTwAdG+B7YsR4=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-06-25T13:11:18.3546563+00:00"}, "S/gr/ANdqnso3HQR/58PbYyEjvWz3ggepSAhFs/7IHw=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-06-25T13:11:18.3785054+00:00"}, "qJSGiZ+LOD7HCab1Vb/UKwkGoDdGWGcCo5JgxrjlVYU=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-06-25T13:11:18.4056416+00:00"}, "A4HVZSFkQ6/tKMn8TAx6jfwc4KDvMrwr4NfUGHUHZU0=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-06-25T13:11:18.435307+00:00"}, "GE/Oox1hAU/my2nTgutXSWWw5ZARQ6EooJXTA9jv+z4=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-06-25T13:11:18.4559123+00:00"}, "2Tc7Uj+6P6SBAqqXNIrrZw4MUaVJPJopDiF72wTiceM=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-06-25T13:11:18.4772931+00:00"}, "HS3qkQWwMDP71XDIwYtQcAv6OnbxyL7krIVy8/4uZ0s=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-06-25T13:11:18.4970904+00:00"}, "8dINMDJwhsZfw0elhn6kDFBeX/FTrEQqs1kf1113mJo=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-06-25T13:11:18.5257631+00:00"}, "i1JrZ2Y2cA67I9HiLqVMzU6tXyjaiJYWdhnB30Pi4g8=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-06-25T13:11:18.5372802+00:00"}, "26Rlr04EabEiLh6hU4n4ZW8Yr0+p9kASJl59prgURDw=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-06-25T13:11:18.5543011+00:00"}, "VitwRWpZZoZPUXfNf9Hp83gciIeM599u0DjKwg3gU5o=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-06-25T13:11:18.5836206+00:00"}, "Lh9oQBeVj33Rn3ZPxcKgv4p7KxoWCaS6PCVhJTfELno=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-06-25T13:11:18.6126406+00:00"}, "CEXGF2hZFB+ulWW97Sz7p8VSsrN1H0RAYHZRNtjFAxw=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-06-25T13:11:18.6304004+00:00"}, "MCGa2TD5H46YSazxO6jLj6SeTJk3xSRjB2luetJf1J4=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-06-25T13:11:18.6460462+00:00"}, "ZDSkrG1iCuWm2ISNUCyS24oe8/j0mQXj7W7tc6Ekeec=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-06-25T13:11:18.6908497+00:00"}, "VlPW8QxZ7oVCM+oJie+wp2yr3FTVhLJVgiESuIoy09Y=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-06-25T13:11:18.7113616+00:00"}, "m1dNhKck3O6229rFxLoMGo78gyBgmYnlnWdZBjbrdyQ=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-06-25T13:11:18.7279413+00:00"}, "Qum9i1JuEC603bbqfvEm6qA+VjgFs+uJYCIqqkgAd9o=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-06-25T13:11:18.7468025+00:00"}, "enyO9AJTpkX/YpPZAZat4/U+kk/2mgVJiviE24YBePo=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-06-25T13:11:18.7776801+00:00"}, "pCgRdVRSLbilEvGem/m5auKZbS5o24JV2oXq9xzEaro=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-06-25T13:11:18.8033877+00:00"}, "he3X3jND+PTLNMqzIXrTmOAGS0lht0xoBaMecoPqu8k=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-06-25T13:11:18.8224777+00:00"}, "8U9VG6gfgT5LuCc3oqoi+gpfxZI3fF+BtSVw4Ku7spk=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-06-25T13:11:18.8514296+00:00"}, "VgbX58gA3M2IoXaUJBAWx6wCFALgktgzLh0AO8z8M2I=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-06-25T13:11:18.9076453+00:00"}, "5OQft9LuK9PkjpcW/eGazir2Z63OzRtZfZ8dpX6dWKs=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-06-25T13:11:18.9422743+00:00"}, "FPCmC33/I6QnCjp0Hacw1WBWqiEiIpqkaw7dhNZkGxY=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-06-25T13:11:18.9932638+00:00"}, "34QShnI3j7a880YXkyGbL63WNgF1635aKAq7P1OcX1E=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-06-25T13:11:19.0471778+00:00"}, "nFD0XwlxtGmnAyD3r1thdZE1mGXNap7jd4ONnba1SB8=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-06-25T13:11:19.0896242+00:00"}, "CeG85OBnMsqFk9Yua7kLUN6HCP9yoYifrL/TDbu8JuQ=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-06-25T13:11:19.146744+00:00"}, "T14bMODE6ZTtegpAoWYIdF4pGAZnDPZlw3HvgOfUB8I=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-06-25T13:11:19.1829373+00:00"}, "j5R9qJU592wOQqM/ZuK2gqm+iQIt8i90Ie6MIPKaqKo=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-06-25T13:11:19.2389809+00:00"}, "sR0fFTNoyHbhQQJDlCGSCKFAqPZOfzuj7+NubjUvR/M=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-06-25T13:11:19.2698393+00:00"}, "k+mzzsy4MEusoZH+cO7XGXvA+DWZ94GCJPMB8RtIg4w=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-06-25T13:11:19.3329658+00:00"}, "oGJAXYPRWExJdEnfY0CxA6EyPyxNDwM+gjtzVGSIcj0=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-06-25T13:11:19.3542539+00:00"}, "YtZVcIiLSKfK8iRR0RNWQAY0V5/VfIMcfjOsYyNkI2U=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-06-25T13:11:19.4149301+00:00"}, "EjXbe2X9nv6vV2zeh9ldqVeV0NzRX7/NiQ6MuIoOcIQ=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-06-25T13:11:19.4309718+00:00"}, "oWYKWW9uruOcX3NqgGY6HW/Dw422/0a8wi4h3qo59Eg=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-06-25T13:11:19.4769522+00:00"}, "D/cpjkSTMYJuiLX999UXOmi9Si3T3htICwUboBKe/fg=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-06-25T13:11:19.4974108+00:00"}, "NdvDyx7LOaYuK+4sIO3p67sU8TFlK2dZUgYkgAktgRw=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-06-25T13:11:19.5480667+00:00"}, "twHUabccNXlFp8LRk1jUAxOzqXwmjja5oqBAbhndN14=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-06-25T13:11:19.5697734+00:00"}, "3SqxyYmsM/PFAqlZbqLEz3+GtgGkA+bONwpc9G616D8=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-06-25T13:11:19.6204037+00:00"}, "k7aQwaHZj0sMn+v6+voJm/D/lMY+BFoF5dQcMsJFfmQ=": {"Identity": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "BlazorApp", "SourceType": "Discovered", "ContentRoot": "c:\\Users\\<USER>\\Documents\\TesteBlazor\\BlazorApp\\wwwroot\\", "BasePath": "_content/BlazorApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-06-25T13:11:19.6411237+00:00"}}, "CachedCopyCandidates": {}}