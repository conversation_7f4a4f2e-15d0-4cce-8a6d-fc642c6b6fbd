﻿@page "/weather"
@rendermode InteractiveServer

<PageTitle>Weather</PageTitle>

<h1>Weather</h1>

<p>This component demonstrates showing data.</p>

@if (forecasts == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <table class="table">
        <thead>
            <tr>
                <th>Date</th>
                <th aria-label="Temperature in Celsius">Temp. (C)</th>
                <th>Wind</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var forecast in forecasts)
            {
                <tr>
                    <td>@forecast.day</td>
                    <td>@forecast.temperature</td>
                    <td>@forecast.wind</td>
                </tr>
            }
        </tbody>
    </table>
}

@code {
    public Forecast[]? forecasts;
    public string? responseString;
    protected override async Task OnInitializedAsync()
    {
        
        // Simulate asynchronous loading to demonstrate streaming rendering
        await Task.Delay(500);
        var startDate = DateOnly.FromDateTime(DateTime.Now);
        await GetData();
        if (!string.IsNullOrEmpty(responseString))
        {
            WeatherCity data = ConvertJsonToWeatherCity(responseString);
            Console.WriteLine(data);
            if (data.forecast != null)
            {
                forecasts = new Forecast[data.forecast.Length];
                for (int i = 0; i < data.forecast.Length; i++)
                {
                    forecasts[i] = data.forecast[i];
                }
            }
        }
    }
    private class WeatherCity{
        public string? temperature { get; set; }
        public string? wind { get; set; }
        public string? description { get; set; }
        public Forecast[]? forecast { get; set; }
    }

    public class Forecast
    {
        public string? day { get; set; }
        public string? temperature { get; set; }
        public string? wind { get; set; }
    }
    // Função para converter string JSON em objeto WeatherCity
    private WeatherCity ConvertJsonToWeatherCity(string json)
    {
        return System.Text.Json.JsonSerializer.Deserialize<WeatherCity>(json);
    }


    private async Task GetData(){
        try{
            HttpClient client = new HttpClient();
            string responseBody = await client.GetStringAsync("https://goweather.xyz/weather/Sertaozinho");
            Console.WriteLine(responseBody);
            responseString = responseBody;
        }
        catch (HttpRequestException e)
        {
            Console.WriteLine("\nException Caught!");
            Console.WriteLine("Message :{0} ", e.Message);
        }
    }
}