.page[b-qgsvw684bo] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-qgsvw684bo] {
    flex: 1;
}

.sidebar[b-qgsvw684bo] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-qgsvw684bo] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-qgsvw684bo]  a, .top-row[b-qgsvw684bo]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-qgsvw684bo]  a:hover, .top-row[b-qgsvw684bo]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-qgsvw684bo]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-qgsvw684bo] {
        justify-content: space-between;
    }

    .top-row[b-qgsvw684bo]  a, .top-row[b-qgsvw684bo]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-qgsvw684bo] {
        flex-direction: row;
    }

    .sidebar[b-qgsvw684bo] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-qgsvw684bo] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-qgsvw684bo]  a:first-child {
        flex: 1;
        text-align: center;
        width: 0;
    }

    .top-row[b-qgsvw684bo], article[b-qgsvw684bo] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}

#blazor-error-ui[b-qgsvw684bo] {
    color-scheme: light only;
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss[b-qgsvw684bo] {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.enviar-btn[b-qgsvw684bo] {
    background: linear-gradient(90deg, #3a0647 0%, #052767 100%);
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 0.6rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(58, 6, 71, 0.08);
    transition: background 0.2s, transform 0.1s;
    margin-top: 0.5rem;
}

.enviar-btn:hover[b-qgsvw684bo] {
    background: linear-gradient(90deg, #052767 0%, #3a0647 100%);
    transform: translateY(-2px) scale(1.04);
}
