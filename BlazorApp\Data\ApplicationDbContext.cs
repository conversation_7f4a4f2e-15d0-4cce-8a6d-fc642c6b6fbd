using Microsoft.EntityFrameworkCore;

namespace BlazorApp.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<UserInfo> Users { get; set; }
        public DbSet<Mensagem> <PERSON>sa<PERSON> { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<UserInfo>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<UserInfo>()
                .HasIndex(u => u.Email)
                .IsUnique();

            modelBuilder.Entity<Mensagem>()
                .HasOne(m => m.CriadoPor)
                .WithMany()
                .HasForeignKey(m => m.CriadoPorId);
        }
    }
}
